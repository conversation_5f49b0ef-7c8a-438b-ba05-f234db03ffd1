'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { PageWrapper } from '@/components/layout';
import { PencilIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { TemplateEditor } from '@/components/notifications/templates/template-editor';
import { useTemplateManagement } from '@/services/notifications';
import { toast } from 'sonner';
import type { 
  NotificationTemplate, 
  UpdateTemplateData 
} from '@/services/notifications/types/notification-types';

interface EditTemplatePageContentProps {
  templateId: string;
}

export function EditTemplatePageContent({ templateId }: EditTemplatePageContentProps) {
  const router = useRouter();
  const { templates, loading, updateTemplate, loadTemplates } = useTemplateManagement();
  const [template, setTemplate] = useState<NotificationTemplate | null>(null);

  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  useEffect(() => {
    if (templates.length > 0) {
      const foundTemplate = templates.find(t => t.id === templateId);
      if (foundTemplate) {
        setTemplate(foundTemplate);
      } else {
        toast.error('Template não encontrado');
        router.push('/academia/configuracoes/templates');
      }
    }
  }, [templates, templateId, router]);

  const handleSave = async (data: UpdateTemplateData) => {
    if (!template) return;
    
    try {
      await updateTemplate(template.id, data);
      toast.success('Template atualizado com sucesso!');
      router.push('/academia/configuracoes/templates');
    } catch (error) {
      console.error('Erro ao atualizar template:', error);
      toast.error('Erro ao atualizar template. Tente novamente.');
    }
  };

  const handleCancel = () => {
    router.push('/academia/configuracoes/templates');
  };

  if (loading || !template) {
    return (
      <PageWrapper
        title="Carregando..."
        subtitle="Carregando dados do template"
        icon={<PencilIcon className="h-6 w-6 text-primary" />}
      >
        <div className="max-w-7xl mx-auto space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <Skeleton className="h-[400px] w-full" />
            </div>
            <div className="space-y-4">
              <Skeleton className="h-[400px] w-full" />
            </div>
          </div>
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper
      title={`Editar: ${template.name}`}
      subtitle="Edite o template de notificação"
      icon={<PencilIcon className="h-6 w-6 text-primary" />}
      actions={
        <Button
          variant="outline"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeftIcon className="h-4 w-4" />
          Voltar
        </Button>
      }
    >
      <div className="max-w-7xl mx-auto">
        <TemplateEditor
          template={template}
          onSave={handleSave}
          onCancel={handleCancel}
          isEditing={true}
        />
      </div>
    </PageWrapper>
  );
}
